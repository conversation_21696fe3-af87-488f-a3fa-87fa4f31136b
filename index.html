<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Code - Learn Programming</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-code"></i>
                <span>Real Code</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#languages" class="nav-link">Languages</a>
                </li>
                <li class="nav-item">
                    <a href="#schematics" class="nav-link">Schematics</a>
                </li>
                <li class="nav-item">
                    <a href="#faq" class="nav-link">FAQ</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">Master Programming with <span class="highlight">Real Code</span></h1>
                <p class="hero-description">
                    Learn programming languages, understand code architecture, and build real-world projects 
                    with our comprehensive learning platform.
                </p>
                <div class="hero-buttons">
                    <a href="#languages" class="btn btn-primary">Start Learning</a>
                    <a href="#schematics" class="btn btn-secondary">View Schematics</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="code-animation">
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Programming Languages Section -->
    <section id="languages" class="languages-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Programming Languages</h2>
                <p class="section-description">
                    Explore popular programming languages and start your coding journey
                </p>
            </div>
            <div class="languages-grid">
                <div class="language-card">
                    <div class="language-icon">
                        <i class="fab fa-js-square"></i>
                    </div>
                    <h3>JavaScript</h3>
                    <p>The language of the web. Build interactive websites and modern applications.</p>
                    <div class="language-features">
                        <span class="feature-tag">Frontend</span>
                        <span class="feature-tag">Backend</span>
                        <span class="feature-tag">Mobile</span>
                    </div>
                    <a href="#" class="learn-btn">Learn JavaScript</a>
                </div>

                <div class="language-card">
                    <div class="language-icon">
                        <i class="fab fa-python"></i>
                    </div>
                    <h3>Python</h3>
                    <p>Versatile and beginner-friendly. Perfect for data science, AI, and web development.</p>
                    <div class="language-features">
                        <span class="feature-tag">Data Science</span>
                        <span class="feature-tag">AI/ML</span>
                        <span class="feature-tag">Web</span>
                    </div>
                    <a href="#" class="learn-btn">Learn Python</a>
                </div>

                <div class="language-card">
                    <div class="language-icon">
                        <i class="fab fa-java"></i>
                    </div>
                    <h3>Java</h3>
                    <p>Enterprise-grade language. Build scalable applications and Android apps.</p>
                    <div class="language-features">
                        <span class="feature-tag">Enterprise</span>
                        <span class="feature-tag">Android</span>
                        <span class="feature-tag">Backend</span>
                    </div>
                    <a href="#" class="learn-btn">Learn Java</a>
                </div>

                <div class="language-card">
                    <div class="language-icon">
                        <i class="fab fa-react"></i>
                    </div>
                    <h3>React</h3>
                    <p>Modern frontend library. Create dynamic user interfaces with component-based architecture.</p>
                    <div class="language-features">
                        <span class="feature-tag">Frontend</span>
                        <span class="feature-tag">SPA</span>
                        <span class="feature-tag">Components</span>
                    </div>
                    <a href="#" class="learn-btn">Learn React</a>
                </div>

                <div class="language-card">
                    <div class="language-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>SQL</h3>
                    <p>Database query language. Manage and manipulate data in relational databases.</p>
                    <div class="language-features">
                        <span class="feature-tag">Database</span>
                        <span class="feature-tag">Queries</span>
                        <span class="feature-tag">Analytics</span>
                    </div>
                    <a href="#" class="learn-btn">Learn SQL</a>
                </div>

                <div class="language-card">
                    <div class="language-icon">
                        <i class="fab fa-node-js"></i>
                    </div>
                    <h3>Node.js</h3>
                    <p>JavaScript runtime for servers. Build fast and scalable backend applications.</p>
                    <div class="language-features">
                        <span class="feature-tag">Backend</span>
                        <span class="feature-tag">APIs</span>
                        <span class="feature-tag">Real-time</span>
                    </div>
                    <a href="#" class="learn-btn">Learn Node.js</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Schematics Section -->
    <section id="schematics" class="schematics-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Code Schematics & Architecture</h2>
                <p class="section-description">
                    Understand how software systems are designed and structured
                </p>
            </div>
            <div class="schematics-content">
                <div class="schematic-tabs">
                    <button class="tab-btn active" data-tab="web-architecture">Web Architecture</button>
                    <button class="tab-btn" data-tab="database-design">Database Design</button>
                    <button class="tab-btn" data-tab="api-patterns">API Patterns</button>
                    <button class="tab-btn" data-tab="deployment">Deployment</button>
                </div>

                <div class="tab-content active" id="web-architecture">
                    <div class="schematic-diagram">
                        <div class="diagram-container">
                            <div class="layer frontend-layer">
                                <h4>Frontend Layer</h4>
                                <div class="components">
                                    <div class="component">React/Vue</div>
                                    <div class="component">HTML/CSS</div>
                                    <div class="component">JavaScript</div>
                                </div>
                            </div>
                            <div class="layer backend-layer">
                                <h4>Backend Layer</h4>
                                <div class="components">
                                    <div class="component">Node.js/Python</div>
                                    <div class="component">REST APIs</div>
                                    <div class="component">Authentication</div>
                                </div>
                            </div>
                            <div class="layer database-layer">
                                <h4>Database Layer</h4>
                                <div class="components">
                                    <div class="component">PostgreSQL</div>
                                    <div class="component">MongoDB</div>
                                    <div class="component">Redis Cache</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="schematic-description">
                        <h3>Modern Web Application Architecture</h3>
                        <p>Learn how modern web applications are structured with separate frontend, backend, and database layers. This architecture provides scalability, maintainability, and clear separation of concerns.</p>
                        <ul>
                            <li>Frontend handles user interface and user experience</li>
                            <li>Backend manages business logic and data processing</li>
                            <li>Database stores and retrieves application data</li>
                            <li>APIs enable communication between layers</li>
                        </ul>
                    </div>
                </div>

                <div class="tab-content" id="database-design">
                    <div class="schematic-diagram">
                        <div class="database-schema">
                            <div class="table-container">
                                <div class="db-table">
                                    <h4>Users</h4>
                                    <div class="table-fields">
                                        <div class="field primary">id (PK)</div>
                                        <div class="field">username</div>
                                        <div class="field">email</div>
                                        <div class="field">created_at</div>
                                    </div>
                                </div>
                                <div class="db-table">
                                    <h4>Projects</h4>
                                    <div class="table-fields">
                                        <div class="field primary">id (PK)</div>
                                        <div class="field foreign">user_id (FK)</div>
                                        <div class="field">title</div>
                                        <div class="field">description</div>
                                    </div>
                                </div>
                                <div class="db-table">
                                    <h4>Code_Files</h4>
                                    <div class="table-fields">
                                        <div class="field primary">id (PK)</div>
                                        <div class="field foreign">project_id (FK)</div>
                                        <div class="field">filename</div>
                                        <div class="field">content</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="schematic-description">
                        <h3>Database Design Principles</h3>
                        <p>Understanding how to design efficient and normalized database schemas is crucial for building scalable applications.</p>
                        <ul>
                            <li>Primary keys uniquely identify records</li>
                            <li>Foreign keys establish relationships between tables</li>
                            <li>Normalization reduces data redundancy</li>
                            <li>Indexes improve query performance</li>
                        </ul>
                    </div>
                </div>

                <div class="tab-content" id="api-patterns">
                    <div class="schematic-diagram">
                        <div class="api-flow">
                            <div class="api-step">
                                <div class="step-icon">1</div>
                                <h4>Client Request</h4>
                                <p>Frontend sends HTTP request</p>
                            </div>
                            <div class="arrow">→</div>
                            <div class="api-step">
                                <div class="step-icon">2</div>
                                <h4>Authentication</h4>
                                <p>Verify user credentials</p>
                            </div>
                            <div class="arrow">→</div>
                            <div class="api-step">
                                <div class="step-icon">3</div>
                                <h4>Business Logic</h4>
                                <p>Process request data</p>
                            </div>
                            <div class="arrow">→</div>
                            <div class="api-step">
                                <div class="step-icon">4</div>
                                <h4>Database Query</h4>
                                <p>Fetch or update data</p>
                            </div>
                            <div class="arrow">→</div>
                            <div class="api-step">
                                <div class="step-icon">5</div>
                                <h4>Response</h4>
                                <p>Return JSON response</p>
                            </div>
                        </div>
                    </div>
                    <div class="schematic-description">
                        <h3>RESTful API Design Patterns</h3>
                        <p>Learn how to design clean, efficient APIs that follow REST principles and industry best practices.</p>
                        <ul>
                            <li>Use HTTP methods appropriately (GET, POST, PUT, DELETE)</li>
                            <li>Implement proper status codes and error handling</li>
                            <li>Design consistent URL patterns and naming conventions</li>
                            <li>Include authentication and authorization layers</li>
                        </ul>
                    </div>
                </div>

                <div class="tab-content" id="deployment">
                    <div class="schematic-diagram">
                        <div class="deployment-flow">
                            <div class="deploy-stage">
                                <i class="fab fa-git-alt"></i>
                                <h4>Version Control</h4>
                                <p>Git Repository</p>
                            </div>
                            <div class="deploy-stage">
                                <i class="fas fa-cogs"></i>
                                <h4>CI/CD Pipeline</h4>
                                <p>Automated Testing & Build</p>
                            </div>
                            <div class="deploy-stage">
                                <i class="fas fa-cloud"></i>
                                <h4>Cloud Platform</h4>
                                <p>AWS, Azure, or GCP</p>
                            </div>
                            <div class="deploy-stage">
                                <i class="fas fa-globe"></i>
                                <h4>Production</h4>
                                <p>Live Application</p>
                            </div>
                        </div>
                    </div>
                    <div class="schematic-description">
                        <h3>Modern Deployment Pipeline</h3>
                        <p>Understand how modern applications are deployed using automated pipelines and cloud infrastructure.</p>
                        <ul>
                            <li>Version control with Git for code management</li>
                            <li>Continuous Integration/Continuous Deployment (CI/CD)</li>
                            <li>Cloud platforms for scalable hosting</li>
                            <li>Monitoring and logging for production applications</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="faq-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Frequently Asked Questions</h2>
                <p class="section-description">
                    Common questions about learning programming and using our platform
                </p>
            </div>
            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What programming language should I learn first?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>For beginners, we recommend starting with <strong>Python</strong> due to its simple syntax and versatility. If you're interested in web development, <strong>JavaScript</strong> is an excellent choice as it's used for both frontend and backend development. The key is to pick one language and stick with it until you're comfortable with programming fundamentals.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How long does it take to learn programming?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>The timeline varies depending on your goals and dedication. To get comfortable with basic programming concepts, expect 3-6 months of consistent practice. To become job-ready, most people need 6-12 months of focused learning and building projects. Remember, programming is a continuous learning journey!</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Do I need a computer science degree to become a programmer?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>No, a computer science degree is not required to become a programmer. Many successful developers are self-taught or have completed coding bootcamps. What matters most is your ability to solve problems, write clean code, and continuously learn new technologies. Building a strong portfolio of projects is often more valuable than formal education.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What's the difference between frontend and backend development?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p><strong>Frontend development</strong> focuses on what users see and interact with - the user interface, user experience, and client-side functionality using HTML, CSS, and JavaScript. <strong>Backend development</strong> involves server-side logic, databases, APIs, and infrastructure that powers the application behind the scenes.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How do I stay motivated while learning to code?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Set small, achievable goals and celebrate your progress. Build projects that interest you personally. Join coding communities and find study partners. Don't compare your progress to others - everyone learns at their own pace. Take breaks when frustrated, and remember that struggling with concepts is a normal part of the learning process.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What tools do I need to start programming?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>You need surprisingly little to get started! A computer with internet access and a text editor or IDE (Integrated Development Environment) like Visual Studio Code, which is free. For web development, you'll also need a web browser. Most programming languages and tools are free to download and use.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How important is math for programming?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Basic math skills are helpful, but advanced mathematics isn't required for most programming jobs. You'll use logical thinking more than complex math. However, certain fields like game development, data science, machine learning, and graphics programming do require stronger mathematical foundations.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Should I learn multiple programming languages at once?</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>It's better to focus on one language initially until you're comfortable with programming fundamentals. Once you understand concepts like variables, functions, loops, and data structures in one language, learning additional languages becomes much easier. Master one, then expand your toolkit.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <div class="contact-content">
                <div class="contact-info">
                    <h2>Ready to Start Your Coding Journey?</h2>
                    <p>Join thousands of learners who are mastering programming with Real Code. Get started today and build the skills you need for a successful career in technology.</p>
                    <div class="contact-features">
                        <div class="feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Interactive coding exercises</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Real-world projects</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Community support</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Expert guidance</span>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form class="newsletter-form">
                        <h3>Get Started Today</h3>
                        <div class="form-group">
                            <input type="text" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <select required>
                                <option value="">Choose your interest</option>
                                <option value="web">Web Development</option>
                                <option value="mobile">Mobile Development</option>
                                <option value="data">Data Science</option>
                                <option value="ai">AI/Machine Learning</option>
                                <option value="game">Game Development</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Start Learning</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-code"></i>
                        <span>Real Code</span>
                    </div>
                    <p>Empowering the next generation of programmers with practical, real-world coding education.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Learn</h4>
                    <ul>
                        <li><a href="#languages">Programming Languages</a></li>
                        <li><a href="#schematics">Code Architecture</a></li>
                        <li><a href="#">Tutorials</a></li>
                        <li><a href="#">Projects</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="#faq">FAQ</a></li>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Community</a></li>
                        <li><a href="#">Blog</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#contact">Contact Us</a></li>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Real Code. All rights reserved. Built with passion for coding education.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
