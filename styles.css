/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-purple: #6366f1;
    --secondary-purple: #8b5cf6;
    --light-purple: #e0e7ff;
    --dark-purple: #4338ca;
    --white: #ffffff;
    --light-gray: #f8fafc;
    --medium-gray: #64748b;
    --dark-gray: #1e293b;
    --gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --shadow: 0 10px 25px rgba(99, 102, 241, 0.1);
    --shadow-hover: 0 20px 40px rgba(99, 102, 241, 0.2);
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(99, 102, 241, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-purple);
}

.nav-logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-gray);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-purple);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-purple);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
}

.highlight {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--medium-gray);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background: var(--gradient);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-secondary {
    background: white;
    color: var(--primary-purple);
    border: 2px solid var(--primary-purple);
}

.btn-secondary:hover {
    background: var(--primary-purple);
    color: white;
    transform: translateY(-2px);
}

/* Hero Animation */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.code-animation {
    width: 300px;
    height: 200px;
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.code-line {
    height: 20px;
    background: var(--light-purple);
    margin: 15px 0;
    border-radius: 10px;
    animation: codeTyping 3s ease-in-out infinite;
}

.code-line:nth-child(1) {
    width: 80%;
    animation-delay: 0s;
}

.code-line:nth-child(2) {
    width: 60%;
    animation-delay: 0.5s;
}

.code-line:nth-child(3) {
    width: 90%;
    animation-delay: 1s;
}

.code-line:nth-child(4) {
    width: 70%;
    animation-delay: 1.5s;
}

@keyframes codeTyping {
    0%, 50% {
        background: var(--light-purple);
    }
    25% {
        background: var(--primary-purple);
    }
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-gray);
}

.section-description {
    font-size: 1.1rem;
    color: var(--medium-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* Languages Section */
.languages-section {
    padding: 80px 0;
    background: var(--white);
}

.languages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.language-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid #f1f5f9;
}

.language-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.language-icon {
    font-size: 3rem;
    color: var(--primary-purple);
    margin-bottom: 1rem;
}

.language-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-gray);
}

.language-card p {
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.language-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature-tag {
    background: var(--light-purple);
    color: var(--primary-purple);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.learn-btn {
    background: var(--gradient);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.learn-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
}

/* Schematics Section */
.schematics-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.schematics-content {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.schematic-tabs {
    display: flex;
    background: var(--light-purple);
    overflow-x: auto;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    font-weight: 600;
    color: var(--primary-purple);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-purple);
    color: white;
}

.tab-content {
    display: none;
    padding: 3rem;
}

.tab-content.active {
    display: block;
}

.schematic-diagram {
    margin-bottom: 2rem;
}

/* Web Architecture Diagram */
.diagram-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.layer {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 15px;
    border-left: 4px solid var(--primary-purple);
}

.layer h4 {
    color: var(--primary-purple);
    margin-bottom: 1rem;
    font-weight: 600;
}

.components {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.component {
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark-gray);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Database Schema */
.database-schema {
    display: flex;
    justify-content: center;
}

.table-container {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    justify-content: center;
}

.db-table {
    background: var(--light-gray);
    border-radius: 10px;
    overflow: hidden;
    min-width: 200px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.db-table h4 {
    background: var(--primary-purple);
    color: white;
    padding: 1rem;
    margin: 0;
    text-align: center;
}

.table-fields {
    padding: 1rem;
}

.field {
    padding: 0.5rem;
    margin: 0.3rem 0;
    border-radius: 5px;
    font-size: 0.9rem;
}

.field.primary {
    background: #fef3c7;
    color: #92400e;
    font-weight: 600;
}

.field.foreign {
    background: #dbeafe;
    color: #1e40af;
    font-weight: 600;
}

/* API Flow */
.api-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.api-step {
    text-align: center;
    min-width: 120px;
}

.step-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin: 0 auto 0.5rem;
}

.api-step h4 {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
    color: var(--dark-gray);
}

.api-step p {
    font-size: 0.8rem;
    color: var(--medium-gray);
}

.arrow {
    font-size: 1.5rem;
    color: var(--primary-purple);
    font-weight: bold;
}

/* Deployment Flow */
.deployment-flow {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 2rem;
}

.deploy-stage {
    text-align: center;
    flex: 1;
    min-width: 150px;
}

.deploy-stage i {
    font-size: 3rem;
    color: var(--primary-purple);
    margin-bottom: 1rem;
}

.deploy-stage h4 {
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
}

.deploy-stage p {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.schematic-description h3 {
    color: var(--primary-purple);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.schematic-description ul {
    list-style: none;
    padding-left: 0;
}

.schematic-description li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: var(--medium-gray);
}

.schematic-description li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-purple);
    font-weight: bold;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: white;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-question:hover {
    color: var(--primary-purple);
}

.faq-question h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.faq-question i {
    transition: transform 0.3s ease;
    color: var(--primary-purple);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
    padding-bottom: 1.5rem;
}

.faq-answer p {
    color: var(--medium-gray);
    line-height: 1.7;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-info h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--dark-gray);
}

.contact-info p {
    color: var(--medium-gray);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.contact-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.feature i {
    color: var(--primary-purple);
    font-size: 1.2rem;
}

.contact-form {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

.contact-form h3 {
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-purple);
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-purple);
}

.footer-logo i {
    margin-right: 0.5rem;
}

.footer-section p {
    color: #94a3b8;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: white;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-purple);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-purple);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--secondary-purple);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 1rem;
    text-align: center;
    color: #94a3b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .languages-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .schematic-tabs {
        flex-direction: column;
    }
    
    .api-flow {
        flex-direction: column;
    }
    
    .arrow {
        transform: rotate(90deg);
    }
    
    .deployment-flow {
        flex-direction: column;
        align-items: center;
    }
}
